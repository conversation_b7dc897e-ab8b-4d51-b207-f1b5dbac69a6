/* Common function that with time should be moved to libbpf */
#ifndef __COMMON_LIBBPF_H
#define __COMMON_LIBBPF_H

struct bpf_pinned_map {
  char const* name;
  char const* filename;
  int map_fd;
};

/*     bpf_prog_load_attr extended */
struct bpf_prog_load_attr_maps {
  char const* file;
  enum bpf_prog_type prog_type;
  enum bpf_attach_type expected_attach_type;
  int ifindex;
  int nr_pinned_maps;
  struct bpf_pinned_map* pinned_maps;
};

int bpf_prog_load_xattr_maps(const struct bpf_prog_load_attr_maps* attr,
                             struct bpf_object** pobj, int* prog_fd);

#endif /* __COMMON_LIBBPF_H */
