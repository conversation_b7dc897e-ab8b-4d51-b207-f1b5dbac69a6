4096 tokens 对应prefill， 128 tokens对应decode
# NCCL
```
[NODE-0] [NODE-0] 开始预热 (4096 tokens)...
[NODE-0] [NODE-0] 开始运行 100 次测试 (4096 tokens)...
[NODE-0] [NODE-0] 完成 10/100 次测试...
[NODE-0] [NODE-0] 完成 20/100 次测试...
[NODE-0] [NODE-0] 完成 30/100 次测试...
[NODE-0] [NODE-0] 完成 40/100 次测试...
[NODE-0] [NODE-0] 完成 50/100 次测试...
[NODE-0] [NODE-0] 完成 60/100 次测试...
[NODE-0] [NODE-0] 完成 70/100 次测试...
[NODE-0] [NODE-0] 完成 80/100 次测试...
[NODE-0] [NODE-0] 完成 90/100 次测试...
[NODE-0] [NODE-0] 完成 100/100 次测试...
[NODE-0] [NODE-0]
[NODE-0] [NODE-0] ===== 使用num_local_tokens=4096的基准测试 (100次平均) =====
[NODE-0] [NODE-0] 平均分发时间: 53.724 ± 1.137 毫秒
[NODE-0] [NODE-0] 最大分发时间: 61.768 ± 3.167 毫秒
[NODE-0] [NODE-0] 平均合并时间: 103.481 ± 3.010 毫秒
[NODE-0] [NODE-0] 最大合并时间: 113.258 ± 2.979 毫秒
[NODE-0] [NODE-0] 总时间: 175.026 毫秒
[NODE-0] [NODE-0]
[NODE-0] [NODE-0] 成功创建gloo进程组用于all_gather_object操作
[NODE-0] [NODE-0] 开始预热 (128 tokens)...
[NODE-0] [NODE-0] 开始运行 100 次测试 (128 tokens)...
[NODE-0] [NODE-0] 完成 10/100 次测试...
[NODE-0] [NODE-0] 完成 20/100 次测试...
[NODE-0] [NODE-0] 完成 30/100 次测试...
[NODE-0] [NODE-0] 完成 40/100 次测试...
[NODE-0] [NODE-0] 完成 50/100 次测试...
[NODE-0] [NODE-0] 完成 60/100 次测试...
[NODE-0] [NODE-0] 完成 70/100 次测试...
[NODE-0] [NODE-0] 完成 80/100 次测试...
[NODE-0] [NODE-0] 完成 90/100 次测试...
[NODE-0] [NODE-0] 完成 100/100 次测试...
[NODE-0] [NODE-0]
[NODE-0] [NODE-0] ===== 使用num_local_tokens=128的基准测试 (100次平均) =====
[NODE-0] [NODE-0] 平均分发时间: 6.328 ± 0.599 毫秒
[NODE-0] [NODE-0] 最大分发时间: 9.372 ± 1.084 毫秒
[NODE-0] [NODE-0] 平均合并时间: 7.260 ± 1.063 毫秒
[NODE-0] [NODE-0] 最大合并时间: 8.248 ± 1.044 毫秒
[NODE-0] [NODE-0] 总时间: 17.620 毫秒
```

# UCCL
```
[NODE-0] [NODE-0] 开始预热 (4096 tokens)...
[NODE-0] [NODE-0] 开始运行 100 次测试 (4096 tokens)...
[NODE-0] [NODE-0] 完成 10/100 次测试...
[NODE-0] [NODE-0] 完成 20/100 次测试...
[NODE-0] [NODE-0] 完成 30/100 次测试...
[NODE-0] [NODE-0] 完成 40/100 次测试...
[NODE-0] [NODE-0] 完成 50/100 次测试...
[NODE-0] [NODE-0] 完成 60/100 次测试...
[NODE-0] [NODE-0] 完成 70/100 次测试...
[NODE-0] [NODE-0] 完成 80/100 次测试...
[NODE-0] [NODE-0] 完成 90/100 次测试...
[NODE-0] [NODE-0] 完成 100/100 次测试...
[NODE-0] [NODE-0]
[NODE-0] [NODE-0] ===== 使用num_local_tokens=4096的基准测试 (100次平均) =====
[NODE-0] [NODE-0] 平均分发时间: 49.312 ± 0.729 毫秒
[NODE-0] [NODE-0] 最大分发时间: 52.694 ± 6.766 毫秒
[NODE-0] [NODE-0] 平均合并时间: 89.611 ± 1.530 毫秒
[NODE-0] [NODE-0] 最大合并时间: 93.521 ± 3.553 毫秒
[NODE-0] [NODE-0] 总时间: 146.215 毫秒
[NODE-0] [NODE-0]
[NODE-0] [NODE-0] 成功创建gloo进程组用于all_gather_object操作
[NODE-0] [NODE-0] 开始预热 (128 tokens)...
[NODE-0] [NODE-0] 开始运行 100 次测试 (128 tokens)...
[NODE-0] [NODE-0] 完成 10/100 次测试...
[NODE-0] [NODE-0] 完成 20/100 次测试...
[NODE-0] [NODE-0] 完成 30/100 次测试...
[NODE-0] [NODE-0] 完成 40/100 次测试...
[NODE-0] [NODE-0] 完成 50/100 次测试...
[NODE-0] [NODE-0] 完成 60/100 次测试...
[NODE-0] [NODE-0] 完成 70/100 次测试...
[NODE-0] [NODE-0] 完成 80/100 次测试...
[NODE-0] [NODE-0] 完成 90/100 次测试...
[NODE-0] [NODE-0] 完成 100/100 次测试...
[NODE-0] [NODE-0]
[NODE-0] [NODE-0] ===== 使用num_local_tokens=128的基准测试 (100次平均) =====
[NODE-0] [NODE-0] 平均分发时间: 5.028 ± 0.660 毫秒
[NODE-0] [NODE-0] 最大分发时间: 6.425 ± 0.967 毫秒
[NODE-0] [NODE-0] 平均合并时间: 4.554 ± 0.300 毫秒
[NODE-0] [NODE-0] 最大合并时间: 4.871 ± 0.707 毫秒
[NODE-0] [NODE-0] 总时间: 11.296 毫秒
```


