workspace(name = "fastsocket")

load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive")
load("@bazel_tools//tools/build_defs/repo:git.bzl", "new_git_repository")
load("@bazel_tools//tools/build_defs/repo:utils.bzl", "maybe")

# NCCL
maybe(
    new_git_repository,
    name = "nccl",
    build_file = "//:bazel/nccl/BUILD.bazel",
    remote = "https://github.com/NVIDIA/nccl.git",
    tag = "v2.13.4-1",
)

# CUDA
maybe(
    new_local_repository,
    name = "local_config_cuda",
    build_file = "//:bazel/cuda/BUILD.bazel",
    path = "/usr/local/cuda",
)

# rules_pkg
http_archive(
    name = "rules_pkg",
    url = "https://github.com/bazelbuild/rules_pkg/archive/main.zip",
    strip_prefix = "rules_pkg-main",
)

load("@rules_pkg//:deps.bzl", "rules_pkg_dependencies")
rules_pkg_dependencies()
