Metadata-Version: 2.4
Name: Authlib
Version: 1.6.0
Summary: The ultimate Python library in building OAuth and OpenID Connect servers and clients.
Author-email: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
License: BSD-3-Clause
Project-URL: Documentation, https://docs.authlib.org/
Project-URL: Purchase, https://authlib.org/plans
Project-URL: Issues, https://github.com/authlib/authlib/issues
Project-URL: Source, https://github.com/authlib/authlib
Project-URL: Donate, https://github.com/sponsors/lepture
Project-URL: Blog, https://blog.authlib.org/
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Security
Classifier: Topic :: Security :: Cryptography
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Internet :: WWW/HTTP :: WSGI :: Application
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: cryptography
Dynamic: license-file

Authlib
=======

The ultimate Python library in building OAuth and OpenID Connect servers.
JWS, JWK, JWA, JWT are included.

Useful Links
------------

1. Homepage: https://authlib.org/
2. Documentation: https://docs.authlib.org/
3. Purchase Commercial License: https://authlib.org/plans
4. Blog: https://blog.authlib.org/
5. More Repositories: https://github.com/authlib
6. Twitter: https://twitter.com/authlib
7. Donate: https://www.patreon.com/lepture

Specifications
--------------

- RFC5849: The OAuth 1.0 Protocol
- RFC6749: The OAuth 2.0 Authorization Framework
- RFC6750: The OAuth 2.0 Authorization Framework: Bearer Token Usage
- RFC7009: OAuth 2.0 Token Revocation
- RFC7515: JSON Web Signature
- RFC7516: JSON Web Encryption
- RFC7517: JSON Web Key
- RFC7518: JSON Web Algorithms
- RFC7519: JSON Web Token
- RFC7521: Assertion Framework for OAuth 2.0 Client Authentication and Authorization Grants
- RFC7523: JSON Web Token (JWT) Profile for OAuth 2.0 Client Authentication and Authorization Grants
- RFC7591: OAuth 2.0 Dynamic Client Registration Protocol
- RFC7592: OAuth 2.0 Dynamic Client Registration Management Protocol
- RFC7636: Proof Key for Code Exchange by OAuth Public Clients
- RFC7638: JSON Web Key (JWK) Thumbprint
- RFC7662: OAuth 2.0 Token Introspection
- RFC8037: CFRG Elliptic Curve Diffie-Hellman (ECDH) and Signatures in JSON Object Signing and Encryption (JOSE)
- RFC8414: OAuth 2.0 Authorization Server Metadata
- RFC8628: OAuth 2.0 Device Authorization Grant
- RFC9101: The OAuth 2.0 Authorization Framework: JWT-Secured Authorization Request (JAR)
- RFC9207: OAuth 2.0 Authorization Server Issuer Identification
- OpenID Connect 1.0
- OpenID Connect Discovery 1.0
- draft-madden-jose-ecdh-1pu-04: Public Key Authenticated Encryption for JOSE: ECDH-1PU

Implementations
---------------

- Requests OAuth 1 Session
- Requests OAuth 2 Session
- Requests Assertion Session
- HTTPX OAuth 1 Session
- HTTPX OAuth 2 Session
- HTTPX Assertion Session
- Flask OAuth 1/2 Client
- Django OAuth 1/2 Client
- Starlette OAuth 1/2 Client
- Flask OAuth 1.0 Server
- Flask OAuth 2.0 Server
- Flask OpenID Connect 1.0
- Django OAuth 1.0 Server
- Django OAuth 2.0 Server
- Django OpenID Connect 1.0

License
-------

Authlib is licensed under BSD. Please see LICENSE for licensing details.

If this license does not fit your company, consider to purchase a commercial
license. Find more information on `Authlib Plans`_.

.. _`Authlib Plans`: https://authlib.org/plans
