diff --git a/src/perftest_communication.c b/src/perftest_communication.c
index 24ed589..ddcea38 100755
--- a/src/perftest_communication.c
+++ b/src/perftest_communication.c
@@ -1985,11 +1985,11 @@ int check_mtu(struct ibv_context *context,struct perftest_parameters *user_param
 		}
 	}
 
-	if (user_param->connection_type == UD && user_param->size > MTU_SIZE(user_param->curr_mtu)) {
+	if (user_param->connection_type == UD && user_param->size > 8192) {
 		if (user_param->test_method == RUN_ALL || !user_param->req_size) {
-			fprintf(stderr," Max msg size in UD is MTU %lu\n",MTU_SIZE(user_param->curr_mtu));
+			fprintf(stderr," Max msg size in UD is MTU %d\n", 8192);
 			fprintf(stderr," Changing to this MTU\n");
-			user_param->size = MTU_SIZE(user_param->curr_mtu);
+			user_param->size = 8192;
 		}
 		else
 		{
diff --git a/src/perftest_parameters.c b/src/perftest_parameters.c
index 92303d6..2ede413 100755
--- a/src/perftest_parameters.c
+++ b/src/perftest_parameters.c
@@ -3676,13 +3676,12 @@ int check_link_and_mtu(struct ibv_context *context,struct perftest_parameters *u
 	else
 		user_param->out_reads = 1;
 
-	if (user_param->connection_type == UD && user_param->size > MTU_SIZE(user_param->curr_mtu)) {
-
+	if (user_param->connection_type == UD && user_param->size > 8192) {
 		if (user_param->test_method == RUN_ALL) {
-			fprintf(stderr," Max msg size in UD is MTU %lu\n",MTU_SIZE(user_param->curr_mtu));
+			fprintf(stderr," Max msg size in UD is MTU %d\n", 8192);
 			fprintf(stderr," Changing to this MTU\n");
 		}
-		user_param->size = MTU_SIZE(user_param->curr_mtu);
+		user_param->size = 8192;
 	}
 
 	/* checking msg size in raw ethernet */
