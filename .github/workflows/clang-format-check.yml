name: Clang Format Check

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  clang-format-check:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install clang-format 14
      run: |
        sudo apt-get update
        sudo apt-get install -y clang-format-14
        sudo ln -sf /usr/bin/clang-format-14 /usr/bin/clang-format

    - name: Show clang-format version
      run: clang-format --version

    - name: Check C++ formatting
      run: |
        set -e

        DIRECTORIES=("afxdp" "efa" "gpu_driven" "rdma" "misc" "p2p" "include" "kvtrans")
        EXTENSIONS=("cpp" "cxx" "cc" "h" "hpp" "cu" "cuh")
        EXCLUDE=("afxdp/lib")

        EXCLUDE_ARGS=()
        for EXC in "${EXCLUDE[@]}"; do
            EXCLUDE_ARGS+=( -path "$EXC" -prune -o )
        done

        FILES=()

        for DIR in "${DIRECTORIES[@]}"; do
          if [ -d "$DIR" ]; then
            for EXT in "${EXTENSIONS[@]}"; do
              while IFS= read -r -d '' FILE; do
                FILES+=("$FILE")
              done < <(find "$DIR" "${EXCLUDE_ARGS[@]}" -type f -name "*.${EXT}" -print0)
            done
          fi
        done

        if [ ${#FILES[@]} -eq 0 ]; then
          echo "No files found to check."
          exit 0
        fi

        echo "Checking formatting on files:"
        for FILE in "${FILES[@]}"; do
          echo "$FILE"
        done

        clang-format --dry-run --Werror "${FILES[@]}"